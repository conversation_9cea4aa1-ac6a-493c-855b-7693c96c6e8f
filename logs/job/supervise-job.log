2025-08-04 16:33:20.932 [localhost-startStop-1] INFO  org.quartz.ee.servlet.QuartzInitializerListener - Quartz Initializer Servlet loaded, initializing Scheduler...
2025-08-04 16:33:20.965 [localhost-startStop-1] INFO  org.quartz.impl.StdSchedulerFactory - Using default implementation for ThreadExecutor
2025-08-04 16:33:20.968 [localhost-startStop-1] INFO  org.quartz.simpl.SimpleThreadPool - Job execution threads will use class loader of thread: localhost-startStop-1
2025-08-04 16:33:20.988 [localhost-startStop-1] INFO  org.quartz.core.SchedulerSignalerImpl - Initialized Scheduler Signaller of type: class org.quartz.core.SchedulerSignalerImpl
2025-08-04 16:33:20.988 [localhost-startStop-1] INFO  org.quartz.core.QuartzScheduler - Quartz Scheduler v.2.2.3 created.
2025-08-04 16:33:20.991 [localhost-startStop-1] INFO  org.quartz.simpl.RAMJobStore - RAMJobStore initialized.
2025-08-04 16:33:20.991 [localhost-startStop-1] INFO  org.quartz.core.QuartzScheduler - Scheduler meta-data: Quartz Scheduler (v2.2.3) 'Scheduler' with instanceId 'SchedulerId'
  Scheduler class: 'org.quartz.core.QuartzScheduler' - running locally.
  NOT STARTED.
  Currently in standby mode.
  Number of jobs executed: 0
  Using thread pool 'org.quartz.simpl.SimpleThreadPool' - with 10 threads.
  Using job-store 'org.quartz.simpl.RAMJobStore' - which does not support persistence. and is not clustered.

2025-08-04 16:33:20.991 [localhost-startStop-1] INFO  org.quartz.impl.StdSchedulerFactory - Quartz scheduler 'Scheduler' initialized from default resource file in Quartz package: 'quartz.properties'
2025-08-04 16:33:20.991 [localhost-startStop-1] INFO  org.quartz.impl.StdSchedulerFactory - Quartz scheduler version: 2.2.3
2025-08-04 16:33:20.991 [localhost-startStop-1] INFO  org.quartz.core.QuartzScheduler - Scheduler Scheduler_$_SchedulerId started.
2025-08-04 16:33:20.992 [localhost-startStop-1] INFO  org.quartz.ee.servlet.QuartzInitializerListener - Scheduler has been started...
2025-08-04 16:33:20.992 [localhost-startStop-1] INFO  org.quartz.ee.servlet.QuartzInitializerListener - Storing the Quartz Scheduler Factory in the servlet context at key: org.quartz.impl.StdSchedulerFactory.KEY
2025-08-04 16:33:26.822 [main] INFO  org.quartz.impl.StdSchedulerFactory - Using default implementation for ThreadExecutor
2025-08-04 16:33:26.823 [main] INFO  org.quartz.simpl.SimpleThreadPool - Job execution threads will use class loader of thread: main
2025-08-04 16:33:26.823 [main] INFO  org.quartz.core.SchedulerSignalerImpl - Initialized Scheduler Signaller of type: class org.quartz.core.SchedulerSignalerImpl
2025-08-04 16:33:26.823 [main] INFO  org.quartz.core.QuartzScheduler - Quartz Scheduler v.2.2.3 created.
2025-08-04 16:33:26.823 [main] INFO  org.quartz.simpl.RAMJobStore - RAMJobStore initialized.
2025-08-04 16:33:26.823 [main] INFO  org.quartz.core.QuartzScheduler - Scheduler meta-data: Quartz Scheduler (v2.2.3) 'schedulerFactoryBean' with instanceId 'SchedulerId'
  Scheduler class: 'org.quartz.core.QuartzScheduler' - running locally.
  NOT STARTED.
  Currently in standby mode.
  Number of jobs executed: 0
  Using thread pool 'org.quartz.simpl.SimpleThreadPool' - with 10 threads.
  Using job-store 'org.quartz.simpl.RAMJobStore' - which does not support persistence. and is not clustered.

2025-08-04 16:33:26.823 [main] INFO  org.quartz.impl.StdSchedulerFactory - Quartz scheduler 'schedulerFactoryBean' initialized from an externally provided properties instance.
2025-08-04 16:33:26.825 [main] INFO  org.quartz.impl.StdSchedulerFactory - Quartz scheduler version: 2.2.3
2025-08-04 16:33:26.827 [main] INFO  org.quartz.core.QuartzScheduler - JobFactory set to: org.springframework.scheduling.quartz.AdaptableJobFactory@6f45be42
2025-08-04 16:42:52.713 [main] INFO  job - 初始化定时任务开始Mon Aug 04 16:42:52 CST 2025
2025-08-04 16:42:53.411 [main] INFO  job - 初始化定时任务结束Mon Aug 04 16:42:53 CST 2025
2025-08-05 16:44:51.745 [main] INFO  job - 初始化定时任务开始Tue Aug 05 16:44:51 CST 2025
2025-08-05 16:44:52.817 [main] INFO  job - 初始化定时任务结束Tue Aug 05 16:44:52 CST 2025
2025-08-05 16:46:33.903 [main] INFO  job - 初始化定时任务开始Tue Aug 05 16:46:33 CST 2025
2025-08-05 16:46:34.484 [main] INFO  job - 初始化定时任务结束Tue Aug 05 16:46:34 CST 2025
2025-08-05 09:18:40.429 [main] INFO  job - 初始化定时任务开始Tue Aug 05 09:18:40 CST 2025
2025-08-05 09:18:41.002 [main] INFO  job - 初始化定时任务结束Tue Aug 05 09:18:41 CST 2025
